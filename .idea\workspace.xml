<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c793481d-c352-468b-aa21-bee20ec00408" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerConfigs">
    <option name="configs">
      <option value="$PROJECT_DIR$/public/assets/libs/eonasdan-bootstrap-datetimepicker/composer.json" />
      <option value="$PROJECT_DIR$/public/assets/libs/jstree/composer.json" />
      <option value="$PROJECT_DIR$/public/assets/libs/bootstrap-table/composer.json" />
      <option value="$PROJECT_DIR$/thinkphp/composer.json" />
    </option>
  </component>
  <component name="ComposerSettings" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/composer.json</pharConfigPath>
    <execution />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="$PROJECT_DIR$/../../../php/php-7.4/php.exe">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/overtrue/pinyin" />
      <path value="$PROJECT_DIR$/vendor/overtrue/socialite" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-installer" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-helper" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/doctrine/cache" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-captcha" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/vendor/mtdowling/cron-expression" />
      <path value="$PROJECT_DIR$/vendor/overtrue/wechat" />
      <path value="$PROJECT_DIR$/vendor/karsonzhang/fastadmin-addons" />
      <path value="$PROJECT_DIR$/vendor/markbaker/complex" />
      <path value="$PROJECT_DIR$/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/vendor/endroid/qr-code" />
      <path value="$PROJECT_DIR$/vendor/pimple/pimple" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/phpmailer/phpmailer" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php70" />
      <path value="$PROJECT_DIR$/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/symfony/psr-http-message-bridge" />
      <path value="$PROJECT_DIR$/vendor/symfony/options-resolver" />
    </include_path>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="30UuYkDrEerrER0PtSRWSbd3yqA" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowPanel.toolwindow.highlight.mappings": "true",
    "WebServerToolWindowPanel.toolwindow.highlight.symlinks": "true",
    "WebServerToolWindowPanel.toolwindow.show.date": "false",
    "WebServerToolWindowPanel.toolwindow.show.permissions": "false",
    "WebServerToolWindowPanel.toolwindow.show.size": "false",
    "last_opened_file_path": "D:/phpEnv/www/2025/湖南沈总地锁小程序",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.keymap",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\phpEnv\www\2025\湖南沈总地锁小程序" />
      <recent name="D:\phpEnv\www\2025\湖南沈总地锁小程序\public\cash" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-PS-251.25410.148" />
        <option value="bundled-php-predefined-a98d8de5180a-c5828cf854d9-com.jetbrains.php.sharedIndexes-PS-251.25410.148" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c793481d-c352-468b-aa21-bee20ec00408" name="Changes" comment="" />
      <created>1753694522305</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753694522305</updated>
      <workItem from="1753694523806" duration="34248000" />
      <workItem from="1753946576519" duration="123000" />
      <workItem from="1753946711856" duration="1198000" />
      <workItem from="1753947925369" duration="7134000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>