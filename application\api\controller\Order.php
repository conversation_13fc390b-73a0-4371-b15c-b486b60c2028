<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use fast\Random;
use think\Validate;
use weixin\WXBizDataCrypt;
use weixin\WeixinPay;
use app\api\controller\order\Order as ord;

/**
 * 订单接口
 */
class Order extends Api {

    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    public function _initialize() {
        parent::_initialize();
    }

    /*
     * 创建订单
     * id 床id
     */

    public function index() {
        $id = input('id', null);
        $ord = new ord();
        $return = $ord->orderAdd($id, $this->auth->id);
        if ($return['success']) {
            $this->success($return['msg'], $return['data']);
        } else {
            $this->error($return['msg']);
        }
    }

    /*
     * 开始订单
     */

    public function orderUse() {
        $order_id = input('order_id', null);
        if ($order_id) {
            $order = db('order')->where(['id' => $order_id])->find();
            if ($order['use_status'] == 1) {
                $order_update = array(
                    'createtime' => time(),
                    'use_status' => 2,
                );
                $res = db('order')->where(['id' => $order_id])->update($order_update);
                if ($res) {
                    db('equipment_info')->where(['id' => $order['equipment_info_id']])->update(array('status' => 2));
                    $this->success('订单开始');
                } else {
                    $this->error('订单开始失败');
                }
            } else {
                $this->error('订单已开始');
            }
        } else {
            $this->error('参数错误');
        }
    }

    /*
     * 离开开锁页面时 验证订单开始了吗，如果未开始删除订单记录
     */

    public function orderUseVerification() {
        $order_id = input('order_id', null);
        if ($order_id) {
            $order = db('order')->where(['id' => $order_id])->find();
            if ($order['use_status'] == 1) {
                //离开页面时 订单未开始
                db('equipment_info')->where(['id' => $order['equipment_info_id']])->update(['status' => 1]);
//                db('order')->where(['id' => $order_id])->update(['use_status' => 3]);
                db('order')->where(['id' => $order_id])->delete();
                $this->success('订单已结束');
            } else {
                $this->success('订单已开始');
            }
        } else {
            $this->error('参数错误');
        }
    }

    /*
     * 还床订单确认
     */

    public function orderInfo() {
        $kpl = input('id', null);
        if ($kpl) {
            $equipment_info = db('equipment_info')->where(['kpl' => $kpl])->find();
            if ($equipment_info) {
                switch ($equipment_info['status']) {
                    case 1:
                        //设备未租用状态
                        $this->error('设备未租用');
                        break;
                    case 2:
                        $equipment = db('equipment')->where(['id' => $equipment_info['equipment_id']])->find();
                        //设备租用中，根据  设备的id  会员id 订单的状态 进行查询有无符合条件的订单
                        $order = db('order')->where(['equipment_info_id' => $equipment_info['id'], 'user_id' => $this->auth->id, 'status' => 1, 'use_status' => 2])->field('id,platform_id,agent_id,hospital_id,departments_id,equipment_id,equipment_info_id,sn as ordercode,user_id,status,pay_types,pay_status,createtime as sdt,updatetime')->find();
                        if ($order) {
                            $order['sdt'] = date('Y-m-d H:i:s', $order['sdt']);
                            //查询医院信息
                            $hospital = db('hospital')->where(['id' => $order['hospital_id']])->find();
                            $order['price'] = $hospital['price'];
                            $order['hourlong'] = $hospital['hourlong'];
                            $equipment_info['hospital_addr'] = $hospital['addr']; //设备地址
                            $equipment_info['equipment_name'] = $equipment_info['deviceno'] . '_' . $equipment['mainname']; //设备名称
                            $return = array(
                                'order' => $order,
                                'device' => $equipment_info,
                                'equipment' => db('equipment')->where(['id' => $order['equipment_id']])->find(),
                            );
                            $this->success('数据查询成功', $return);
                        } else {
                            $this->error('无该设备租用信息');
                        }
                        break;
                    case 3:
                        $this->error('设备故障');
                        break;
                    default:
                        $this->error('系统错误');
                        break;
                }
            } else {
                $this->error('设备不存在');
            }
        } else {
            $this->error('参数错误');
        }
    }

    /*
     * 结束订单 - 会员主动扫码结束
     * ordercode 订单编号
     */

    public function orderUpdateStatus() {
        $sn = $this->request->param('ordercode', null);
        $time = time();
        $order_info = db('order')->where(['sn' => $sn])->find();
        $ord = new ord();
        $return = $ord->orderEnd($order_info);
        if ($return['success']) {
            $order_info = db('order')->where(['sn' => $sn])->find();
            $order_info['createtime'] = date('Y-m-d H:i:s', $order_info['createtime']);
            $order_info['returntime'] = date('Y-m-d H:i:s', $order_info['returntime']);
            $order_info['answer_return_time'] = date('Y-m-d H:i:s', $order_info['answer_return_time']);

            $hospital = db('hospital')->where(['id' => $order_info['hospital_id']])->find();
            $equipment = db('equipment')->where(['id' => $order_info['equipment_id']])->find();
            $equipment_info = db('equipment_info')->where(['id' => $order_info['equipment_info_id']])->find();

            $order_info['hospital_name'] = $hospital['name'];
            $order_info['hospital_addr'] = $hospital['addr'];
            $order_info['hospital_contract_price'] = $hospital['contract_price'];
            $order_info['equipment_name'] = $equipment_info['deviceno'] . '_' . $equipment['mainname']; //设备名称


            $this->success('查询成功', $order_info);
        } else {
            $this->error($return['msg']);
        }
    }

    /*
     * 订单支付 - 微信支付
     */

    public function orderWeixin() {
        $sn = input('ordercode', null);
        $order_info = db('order')->where(['sn' => $sn])->find();

        if ($order_info) {
            if ($order_info['status'] == 2) {
                $pay = db('pay')->where(['order_id' => $order_info['id']])->find();
                if (!$pay) {
                    //生成支付订单
                    $pay_data = array(
                        'sn' => $this->getOrdersn('pay'),
                        'user_id' => $order_info['user_id'],
                        'types' => 2,
                        'status' => 1,
                        'money' => $order_info['money'],
                        'order_id' => $order_info['id'],
                        'createtime' => time(),
                        'updatetime' => time(),
                    );
                    $pay_id = db('pay')->insertGetId($pay_data);
                    $pay = db('pay')->where(['id' => $pay_id])->find();
                }
                if ($pay) {
                    $return = $this->payment($pay['id']);
                    if ($return['success']) {
                        $this->success('加载成功', $return['data']);
                    } else {
                        $this->error($return['msg']);
                    }
                } else {
                    $this->error('支付信息不存在');
                }
            } else {
                $this->error('订单状态错误');
            }
        } else {
            $this->error('订单不存在');
        }
    }

    /*
     * 订单支付 - 短时免单
     */

    public function orderFreeSheet() {
        $sn = input('ordercode', null);
        $order_info = db('order')->where(['sn' => $sn])->find();

        $time = time();
        $order_update_data = array(
            'status' => 3,
            'pay_types' => 0,
//            'pay_status' => 3,
            'updatetime' => time(),
        );
        $res = db('order')->where(['id' => $order_info['id']])->update($order_update_data);
        if ($res) {
            $this->success('操作成功');
        } else {
            $this->error('更新状态失败');
        }
    }

    /*
     * 保证金充值
     */

    public function bondRecharge() {
        $user = db('user')->where(['id' => $this->uid])->find();
        if ($user['deposit'] > 0) {
            $this->error('已充值保证金');
        } else {
            $pay = db('pay')->where(['user_id' => $this->uid, 'status' => ['in', '2,5'], 'types' => 1])->find();
            if ($pay) {
                $this->error('已充值保证金');
            } else {
                $bond = db('config')->where(['id' => 18])->value('value');
                //生成支付订单
                $pay_data = array(
                    'sn' => $this->getOrdersn('pay'),
                    'user_id' => $this->uid,
                    'types' => 1,
                    'status' => 1,
                    'money' => $bond,
                    'createtime' => time(),
                    'updatetime' => time(),
                );
                $res = db('pay')->insertGetId($pay_data);
                if ($res) {
                    $return = $this->payment($res);
                    if ($return['success']) {
                        $this->success('加载成功', $return['data']);
                    } else {
                        $this->error($return['msg']);
                    }
                } else {
                    $this->error('支付记录创建失败');
                }
            }
        }
    }

    /*
     * 发起支付
     */

    public function payment($id) {
        $return = array(
            'success' => FALSE,
        );
        $pay = db('pay')->where(['id' => $id])->find();

        if ($pay) {
            if ($pay['status'] == 1) {
                $payconfig = config('wxali.wx');
                $user = db('user')->where(['id' => $pay['user_id']])->find();
                $appid = $payconfig['xcx']['appid'];
                $openid = $user['openid'];
                $mch_id = $payconfig['sh']['mch_id'];
                $key = $payconfig['sh']['key'];
                $money = $pay['money'];
                $order_sn = $pay['sn'];
//                $money = 0.01;
                $weixinPay = new WeixinPay($appid, $openid, $mch_id, $key, $money, $order_sn);
                $mentPay = $weixinPay->pay();
                $return['success'] = true;
                $return['msg'] = '发起成功';
                $return['data'] = $mentPay;
            } else {
                $return['msg'] = '订单已支付';
            }
        } else {
            $return['msg'] = '订单不存在';
        }
        return $return;
    }

    //首页扫码进入判断设备状态
    public function shebeiStatus() {

        $kpl = input('id', null);
        if ($kpl) {
            $equipment_info = db('equipment_info')->where(['kpl' => $kpl])->find();
            if ($equipment_info) {
                $this->success('数据查询成功', $equipment_info);
            } else {
                $this->error('设备不存在');
            }
        } else {
            $this->error('参数错误');
        }
    }

    /*
     * 获得我的订单
     * status 状态
     */

    public function getMyOrder() {
        $status = input('status', 2);
        $where = [
            'user_id' => $this->auth->id,
//            'status' => $status, 
            'use_status' => 2,
        ];
        if ($status) {
            $where['status'] = $status;
        }
        $order = db('order')->where($where)->order('id', 'desc')->select();
        foreach ($order as $k => $v) {
            $order[$k]['createtime'] = date('Y-m-d H:i:s', $order[$k]['createtime']);
            $order[$k]['returntime'] = date('Y-m-d H:i:s', $order[$k]['returntime']);
            $equipment_info = db('equipment_info')->where(['id' => $order[$k]['equipment_info_id']])->find();
            $equipment = db('equipment')->where(['id' => $equipment_info['equipment_id']])->find();
            $hospital = db('hospital')->where(['id' => $equipment['hospitals_id']])->find();
            //$order[$k]['equipment_info_deviceno'] = db('equipment_info')->where(['id' => $order[$k]['equipment_info_id']])->value('deviceno');
            $order[$k]['equipment_name'] = $equipment_info['deviceno'] . '_' . $equipment['mainname'];
            $order[$k]['hospital_addr'] = $hospital['addr'];
            if ($order[$k]['status'] == 2) {
                if ($order[$k]['pay_status'] == 1) {
                    $order[$k]['statusname'] = '立即支付';
                } else {
                    $order[$k]['statusname'] = '点击免单';
                }
            } else if ($order[$k]['status'] == 1) {
                $order[$k]['statusname'] = '使用中';
            } else {
                $order[$k]['statusname'] = '已完成';
            }
            switch ($order[$k]['pay_status']) {
                case 1:
                    $order[$k]['pay_types_str'] = '微信支付';
                    break;
                case 2:
                    $order[$k]['pay_types_str'] = '故障免单';
                    break;
                case 3:
                    $order[$k]['pay_types_str'] = '短时免单';
                    break;
                case 4:
                    $order[$k]['pay_types_str'] = '系统免单';
                    break;
                case 5:
                    $order[$k]['pay_types_str'] = '套餐抵扣';
                    break;
                default:
                    $order[$k]['pay_types_str'] = '无';
                    break;
            }
        }
        $count = count($order);
        $return = array(
            'data' => $order,
            'count' => $count
        );
        $this->success('数据加载成功', $return);
    }

    //随机生成订单编号
    private function getOrdersn($surface = 'order') {
        $no = 'ord' . date('YmdHis') . rand(10000000, 99999999);
        if (db($surface)->where('sn', $no)->find()) {
            $no = $this->getOrdersn();
        } else {
            return $no;
        }
    }

    /**
     * 创建充值订单
     */
    public function rechargeCreate() {
        $amount = input('amount', 0);

        // 记录创建充值订单日志
        $log_data = [
            'type' => 'recharge_create_start',
            'user_id' => $this->auth->id,
            'amount' => $amount,
            'time' => date('Y-m-d H:i:s'),
            'ip' => request()->ip()
        ];
        trace($log_data, '充值订单创建开始');

        // 验证充值金额
        if (!$amount || $amount <= 0) {
            $log_data['type'] = 'recharge_create_error';
            $log_data['error'] = '充值金额必须大于0';
            trace($log_data, '充值金额验证失败');
            $this->error('充值金额必须大于0');
        }

        // 验证充值金额范围（可根据业务需求调整）
        if ($amount < 0.01 || $amount > 10000) {
            $log_data['type'] = 'recharge_create_error';
            $log_data['error'] = '充值金额范围为0.01-10000元';
            trace($log_data, '充值金额范围验证失败');
            $this->error('充值金额范围为0.01-10000元');
        }

        try {
            // 生成充值订单
            $order_sn = $this->getRechargeOrderSn();
            $recharge_data = [
                'order_sn' => $order_sn,
                'user_id' => $this->auth->id,
                'amount' => $amount,
                'status' => '1', // 待支付
                'createtime' => time(),
                'updatetime' => time(),
                'memo' => '用户余额充值'
            ];

            $recharge_id = db('recharge_order')->insertGetId($recharge_data);
            if (!$recharge_id) {
                $log_data['type'] = 'recharge_create_error';
                $log_data['error'] = '创建充值订单失败';
                trace($log_data, '充值订单创建失败');
                $this->error('创建充值订单失败');
            }

            $log_data['recharge_id'] = $recharge_id;
            $log_data['order_sn'] = $order_sn;
            trace($log_data, '充值订单创建成功');

            // 生成支付记录
            $pay_data = [
                'sn' => $this->getOrdersn('pay'),
                'user_id' => $this->auth->id,
                'types' => 4, // 余额充值
                'pay_type' => 1, // 微信支付
                'status' => 1, // 未支付
                'money' => $amount,
                'order_id' => $recharge_id, // 关联充值订单ID
                'createtime' => time(),
                'updatetime' => time(),
                'really_money' => $amount,
            ];

            $pay_id = db('pay')->insertGetId($pay_data);
            if (!$pay_id) {
                $log_data['type'] = 'recharge_create_error';
                $log_data['error'] = '创建支付记录失败';
                trace($log_data, '支付记录创建失败');
                $this->error('创建支付记录失败');
            }

            $log_data['pay_id'] = $pay_id;
            $log_data['pay_sn'] = $pay_data['sn'];
            $log_data['type'] = 'recharge_create_success';
            trace($log_data, '充值订单和支付记录创建完成');

            $return = [
                'recharge_id' => $recharge_id,
                'pay_id' => $pay_id,
                'order_sn' => $order_sn,
                'amount' => $amount
            ];

            $this->success('创建充值订单成功', $return);

        } catch (\Exception $e) {
            $log_data['type'] = 'recharge_create_exception';
            $log_data['error'] = $e->getMessage();
            $log_data['trace'] = $e->getTraceAsString();
            trace($log_data, '充值订单创建异常');
            $this->error('系统异常，请稍后重试');
        }
    }

    /**
     * 发起充值支付
     */
    public function rechargePay() {
        $pay_id = input('pay_id', 0);

        // 记录支付发起日志
        $log_data = [
            'type' => 'recharge_pay_start',
            'user_id' => $this->auth->id,
            'pay_id' => $pay_id,
            'time' => date('Y-m-d H:i:s'),
            'ip' => request()->ip()
        ];
        trace($log_data, '充值支付发起开始');

        if (!$pay_id) {
            $log_data['type'] = 'recharge_pay_error';
            $log_data['error'] = '支付记录ID不能为空';
            trace($log_data, '支付ID验证失败');
            $this->error('支付记录ID不能为空');
        }

        $pay = db('pay')->where(['id' => $pay_id, 'user_id' => $this->auth->id])->find();
        if (!$pay) {
            $log_data['type'] = 'recharge_pay_error';
            $log_data['error'] = '支付记录不存在';
            trace($log_data, '支付记录查询失败');
            $this->error('支付记录不存在');
        }

        $log_data['pay_info'] = $pay;
        trace($log_data, '支付记录查询成功');

        if ($pay['status'] != 1) {
            $log_data['type'] = 'recharge_pay_error';
            $log_data['error'] = '订单状态异常，当前状态：' . $pay['status'];
            trace($log_data, '支付状态验证失败');
            $this->error('订单状态异常');
        }

        if ($pay['types'] != 4) {
            $log_data['type'] = 'recharge_pay_error';
            $log_data['error'] = '非充值订单，当前类型：' . $pay['types'];
            trace($log_data, '支付类型验证失败');
            $this->error('非充值订单');
        }

        try {
            // 获取微信支付配置
            $payconfig = config('wxali.wx');
            $user = db('user')->where(['id' => $this->auth->id])->find();

            $appid = $payconfig['xcx']['appid'];
            $openid = $user['openid'];
            $mch_id = $payconfig['sh']['mch_id'];
            $key = $payconfig['sh']['key'];
            $money = $pay['money'];
            $order_sn = $pay['sn'];

            $log_data['pay_config'] = [
                'appid' => $appid,
                'openid' => $openid,
                'mch_id' => $mch_id,
                'money' => $money,
                'order_sn' => $order_sn
            ];
            trace($log_data, '微信支付配置准备完成');

            // 发起微信支付
            $weixinPay = new WeixinPay($appid, $openid, $mch_id, $key, $money, $order_sn);
            $payResult = $weixinPay->pay();

            $log_data['type'] = 'recharge_pay_success';
            $log_data['pay_result'] = $payResult;
            trace($log_data, '微信支付发起成功');

            $return = [
                'success' => true,
                'msg' => '发起支付成功',
                'data' => $payResult
            ];

            $this->success($return['msg'], $return['data']);

        } catch (\Exception $e) {
            $log_data['type'] = 'recharge_pay_exception';
            $log_data['error'] = $e->getMessage();
            $log_data['trace'] = $e->getTraceAsString();
            trace($log_data, '充值支付发起异常');
            $this->error('支付发起失败，请稍后重试');
        }
    }

    /**
     * 生成充值订单号
     */
    private function getRechargeOrderSn() {
        $no = 'RC' . date('YmdHis') . rand(100000, 999999);
        if (db('recharge_order')->where('order_sn', $no)->find()) {
            return $this->getRechargeOrderSn();
        }
        return $no;
    }

}
