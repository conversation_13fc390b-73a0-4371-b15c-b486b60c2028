<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/addons/command/application" isTestSource="false" packagePrefix="app" />
      <sourceFolder url="file://$MODULE_DIR$/application" isTestSource="false" packagePrefix="app" />
      <sourceFolder url="file://$MODULE_DIR$/extend" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/thinkphp/library/think" isTestSource="false" packagePrefix="think\" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/endroid/qr-code" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/karsonzhang/fastadmin-addons" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/markbaker/complex" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/markbaker/matrix" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/mtdowling/cron-expression" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/overtrue/pinyin" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/overtrue/socialite" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/overtrue/wechat" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/paragonie/random_compat" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpmailer/phpmailer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpoffice/phpspreadsheet" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/pimple/pimple" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/options-resolver" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php70" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/psr-http-message-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-captcha" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-helper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-installer" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>