# 余额充值功能实现任务

## 任务概述
实现用户余额充值接口，支持微信小程序支付，充值成功后增加金额到fa_user表的money字段。

## 需求分析
1. 用户提交充值时，先生成充值订单，然后在pay表里新增一条记录
2. 在回调中，更改订单状态，将支付成功的pay表信息更新到recharge_order表，且留下用户资金变动记录
3. 复用现有的WeixinPay.php支付类和回调地址

## 实现方案
采用最小改动方案，复用现有支付流程：
- 修改User.php模型的money()方法，兼容新字段
- 在Order.php中新增充值相关方法
- 修改Pay.php的notify()方法，新增types=4处理逻辑

## 已完成的步骤

### 1. 修改User.php模型 ✅
- 文件：application/common/model/User.php
- 修改：money()方法增加type和order_id参数支持
- 保持向后兼容性

### 2. 新增充值接口 ✅
- 文件：application/api/controller/Order.php
- 新增方法：
  - rechargeCreate() - 创建充值订单
  - rechargePay() - 发起充值支付
  - getRechargeOrderSn() - 生成充值订单号

### 3. 修改支付回调 ✅
- 文件：application/api/controller/Pay.php
- 修改：notify()方法新增types=4处理逻辑
- 功能：更新充值订单状态，更新用户余额，记录资金变动

## 接口说明

### 创建充值订单
- 接口：POST /api/order/rechargeCreate
- 参数：amount (充值金额)
- 返回：充值订单信息和支付记录ID

### 发起充值支付
- 接口：POST /api/order/rechargePay  
- 参数：pay_id (支付记录ID)
- 返回：微信支付参数

## 数据库设计
- fa_recharge_order: 充值订单表
- fa_pay: 支付记录表 (types=4表示余额充值)
- fa_user: 用户表 (money字段存储余额)
- fa_user_money_log: 用户资金变动记录表 (type=1表示充值)

## 日志记录
为了方便排查问题，已在关键节点添加详细日志：

### 日志记录方式
使用项目标准的 `trace()` 函数记录日志，符合项目规范：
- 第一个参数：要记录的数据（数组或变量）
- 第二个参数：日志描述信息

### 日志内容说明
1. **充值订单创建日志** - 记录充值订单创建过程
   - 用户ID、充值金额、IP地址
   - 订单创建结果、支付记录创建结果
   - 错误信息和异常堆栈
   - 日志标识：'充值订单创建开始'、'充值订单创建成功'等

2. **充值支付发起日志** - 记录支付发起过程
   - 支付记录验证过程
   - 微信支付配置信息
   - 支付发起结果
   - 日志标识：'充值支付发起开始'、'微信支付发起成功'等

3. **充值回调处理日志** - 记录支付回调处理
   - 微信回调原始数据
   - 充值订单更新结果
   - 用户余额变更前后对比
   - 处理成功/失败状态
   - 日志标识：'充值回调处理开始'、'充值回调处理成功'等

### 日志查看方式
根据项目配置，trace()日志通常记录在：
- ThinkPHP默认日志目录：`./runtime/log/`
- 可通过项目日志配置查看具体位置
- 建议查看项目的日志配置文件确认日志存储位置

## 测试建议
1. 测试充值订单创建
2. 测试微信支付发起
3. 测试支付回调处理
4. 验证用户余额更新
5. 验证资金变动记录
6. 通过日志文件排查问题
