checks:
    php: true

coding_style:
    php:
        spaces:
            before_parentheses:
                closure_definition: true
            around_operators:
                concatenation: true

build:
    nodes:
        analysis:
            tests:
                override:
                    - php-scrutinizer-run

tools:
    external_code_coverage:
        timeout: 3600

build_failure_conditions:
    - 'elements.rating(<= C).new.exists'                        # No new classes/methods with a rating of C or worse allowed
    - 'issues.severity(>= MAJOR).new.exists'                    # New issues of major or higher severity
    - 'project.metric_change("scrutinizer.test_coverage", < 0)' # Code Coverage decreased from previous inspection
    - 'patches.label("Unused Use Statements").new.exists'       # No new unused imports patches allowed
