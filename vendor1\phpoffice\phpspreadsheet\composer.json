{"name": "phpoffice/phpspreadsheet", "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "keywords": ["PHP", "OpenXML", "Excel", "xlsx", "xls", "ods", "gnumeric", "spreadsheet"], "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "type": "library", "license": "LGPL-2.1-or-later", "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "scripts": {"check": ["php-cs-fixer fix --ansi --dry-run --diff", "phpcs --report-width=200 samples/ src/ tests/ --ignore=samples/Header.php --standard=PSR2 -n", "phpunit --color=always"], "fix": ["php-cs-fixer fix --ansi"], "versions": ["phpcs --report-width=200 samples/ src/ tests/ --ignore=samples/Header.php --standard=PHPCompatibility --runtime-set testVersion 5.6- -n"]}, "require": {"php": "^5.6|^7.0", "ext-ctype": "*", "ext-dom": "*", "ext-gd": "*", "ext-iconv": "*", "ext-fileinfo": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-SimpleXML": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "psr/simple-cache": "^1.0", "markbaker/complex": "^1.4", "markbaker/matrix": "^1.1"}, "require-dev": {"tecnickcom/tcpdf": "^6.2", "phpunit/phpunit": "^5.7", "doctrine/instantiator": "^1.0.0", "dompdf/dompdf": "^0.8.0", "mpdf/mpdf": "^7.0.0", "jpgraph/jpgraph": "^4.0", "friendsofphp/php-cs-fixer": "@stable", "phpcompatibility/php-compatibility": "^8.0", "squizlabs/php_codesniffer": "^3.3"}, "suggest": {"mpdf/mpdf": "Option for rendering PDF with PDF Writer", "dompdf/dompdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers"}, "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "autoload-dev": {"psr-4": {"PhpOffice\\PhpSpreadsheetTests\\": "tests/PhpSpreadsheetTests"}}}