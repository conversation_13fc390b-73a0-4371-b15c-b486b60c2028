{"name": "karsonzhang/fastadmin-addons", "description": "addons package for fastadmin", "homepage": "https://github.com/karsonzhang/fastadmin-addons", "license": "Apache-2.0", "minimum-stability": "dev", "version": "1.1.7", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xiaobo.sun", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/karsonzhang/fastadmin-addons/issues"}, "require": {"php": ">=5.4.0", "topthink/think-helper": ">=1.0.4", "topthink/think-installer": ">=1.0.10"}, "autoload": {"psr-4": {"think\\": "src/"}, "files": ["src/common.php"]}, "extra": {"think-config": {"addons": "src/config.php"}}}