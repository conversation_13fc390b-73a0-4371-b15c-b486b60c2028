<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MessDetector">
    <phpmd_settings>
      <phpmd_by_interpreter asDefaultInterpreter="true" interpreter_id="9faa1d79-f3e3-477e-9f27-cb33d26760b6" timeout="30000" />
    </phpmd_settings>
  </component>
  <component name="MessDetectorOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="highlightLevel" value="WARNING" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpCodeSniffer">
    <phpcs_settings>
      <phpcs_by_interpreter asDefaultInterpreter="true" interpreter_id="9faa1d79-f3e3-477e-9f27-cb33d26760b6" timeout="30000" />
    </phpcs_settings>
  </component>
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/overtrue/pinyin" />
      <path value="$PROJECT_DIR$/vendor/overtrue/socialite" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-installer" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-helper" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/doctrine/cache" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-captcha" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/vendor/mtdowling/cron-expression" />
      <path value="$PROJECT_DIR$/vendor/overtrue/wechat" />
      <path value="$PROJECT_DIR$/vendor/karsonzhang/fastadmin-addons" />
      <path value="$PROJECT_DIR$/vendor/markbaker/complex" />
      <path value="$PROJECT_DIR$/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/vendor/endroid/qr-code" />
      <path value="$PROJECT_DIR$/vendor/pimple/pimple" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/phpmailer/phpmailer" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php70" />
      <path value="$PROJECT_DIR$/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/symfony/psr-http-message-bridge" />
      <path value="$PROJECT_DIR$/vendor/symfony/options-resolver" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="5.4.0">
    <option name="suggestChangeDefaultLanguageLevel" value="false" />
  </component>
  <component name="PhpStan">
    <PhpStan_settings>
      <phpstan_by_interpreter asDefaultInterpreter="true" interpreter_id="9faa1d79-f3e3-477e-9f27-cb33d26760b6" timeout="60000" />
    </PhpStan_settings>
  </component>
  <component name="PhpStanOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="Psalm">
    <Psalm_settings>
      <psalm_fixer_by_interpreter asDefaultInterpreter="true" interpreter_id="9faa1d79-f3e3-477e-9f27-cb33d26760b6" timeout="60000" />
    </Psalm_settings>
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
</project>