{"name": "mtdowling/cron-expression", "type": "library", "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0"}, "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/Cron/"}}}